name: Publish to PyPI

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      test_pypi:
        description: 'Publish to Test PyPI first'
        required: false
        default: true
        type: boolean

jobs:
  build:
    name: Build distribution packages
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install hatch
    
    - name: Build package
      run: hatch build
    
    - name: Store the distribution packages
      uses: actions/upload-artifact@v4
      with:
        name: python-package-distributions
        path: dist/

  publish-to-test-pypi:
    name: Publish to Test PyPI
    if: github.event_name == 'workflow_dispatch' && github.event.inputs.test_pypi == 'true'
    needs: build
    runs-on: ubuntu-latest
    
    environment:
      name: test-pypi
      url: https://test.pypi.org/p/microsoft_sql_server_mcp
    
    permissions:
      id-token: write  # IMPORTANT: mandatory for trusted publishing
    
    steps:
    - name: Download all the dists
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions
        path: dist/
    
    - name: Publish to Test PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        repository-url: https://test.pypi.org/legacy/
        skip-existing: true

  publish-to-pypi:
    name: Publish to PyPI
    if: github.event_name == 'release' || (github.event_name == 'workflow_dispatch' && github.event.inputs.test_pypi == 'false')
    needs: build
    runs-on: ubuntu-latest
    
    environment:
      name: pypi
      url: https://pypi.org/p/microsoft_sql_server_mcp
    
    permissions:
      id-token: write  # IMPORTANT: mandatory for trusted publishing
    
    steps:
    - name: Download all the dists
      uses: actions/download-artifact@v4
      with:
        name: python-package-distributions
        path: dist/
    
    - name: Publish to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1