name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  test:
    name: Test Python ${{ matrix.python-version }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: ['3.11', '3.12']
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install system dependencies (Ubuntu)
      if: matrix.os == 'ubuntu-latest'
      run: |
        sudo apt-get update
        sudo apt-get install -y freetds-dev
    
    - name: Install system dependencies (macOS)
      if: matrix.os == 'macos-latest'
      run: |
        brew install freetds
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
        pip install -e .
    
    - name: Lint with ruff
      run: |
        pip install ruff
        ruff check src tests
    
    - name: Type check with mypy
      run: |
        pip install mypy
        mypy src --ignore-missing-imports
    
    - name: Test with pytest
      run: |
        pytest tests -v --tb=short
      env:
        MSSQL_SERVER: localhost
        MSSQL_USER: test
        MSSQL_PASSWORD: test
        MSSQL_DATABASE: test
    
    - name: Check package build
      run: |
        pip install hatch
        hatch build
        ls -la dist/

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install bandit[toml] safety
    
    - name: Run Bandit security scan
      run: bandit -r src -f json -o bandit-report.json || true
    
    - name: Run Safety check
      run: |
        pip install -r requirements.txt
        safety check --json || true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-reports
        path: |
          bandit-report.json
          
  docker-build:
    name: Docker Build Test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image
      run: |
        docker build -t mssql-mcp-server:test .
    
    - name: Test Docker image
      run: |
        docker run --rm mssql-mcp-server:test python --version